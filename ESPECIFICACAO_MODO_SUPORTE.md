# 📋 ESPECIFICAÇÃO TÉCNICA - MODO SUPORTE WHATSAPP

## 🎯 Objetivo
Implementar um modo "Suporte" na extensão Chrome WhatsApp, permitindo alternar entre modo "Vendas" (atual) e "Suporte", reutilizando toda a infraestrutura existente.

---

## 🔧 MODIFICAÇÕES NA EXTENSÃO CHROME

### 1. Sistema de Configuração

#### 1.1 Estrutura de Dados (chrome.storage)
**Arquivo:** `WhatsappChrome2/config.js` (novo)
```javascript
const DEFAULT_CONFIG = {
  mode: 'vendas', // 'vendas' | 'suporte'
  vendas: {
    baseUrl: 'https://admin.promokit.com.br/crm/whatsapp-assistant/',
    title: 'WhatsApp Vendas',
    icon: 'fas fa-chart-line',
    primaryColor: '#25d366',
    secondaryColor: '#128c7e'
  },
  suporte: {
    baseUrl: 'https://admin.promokit.com.br/suporte/whatsapp-assistant/',
    title: 'WhatsApp Suporte', 
    icon: 'fas fa-headset',
    primaryColor: '#ff6b35',
    secondaryColor: '#f7931e'
  }
};
```

#### 1.2 Modificações no Manifest
**Arquivo:** `WhatsappChrome2/manifest.json`
```json
{
  "permissions": [
    "storage",
    "tabs",
    "declarativeNetRequest",
    "webNavigation"
  ],
  "host_permissions": [
    "https://web.whatsapp.com/*",
    "https://admin.promokit.com.br/*",
    "https://*.promokit.com.br/*"
  ]
}
```

### 2. Interface do Side Panel

#### 2.1 Seletor de Modo
**Arquivo:** `WhatsappChrome2/sidepanel.html`
```html
<!-- Adicionar após .header-content -->
<div class="mode-section">
  <h2 class="section-title">Modo de Operação</h2>
  <div class="mode-selector">
    <div class="mode-option" data-mode="vendas">
      <i class="fas fa-chart-line"></i>
      <div class="mode-info">
        <span class="mode-name">Vendas</span>
        <span class="mode-desc">CRM e Leads</span>
      </div>
    </div>
    <div class="mode-option" data-mode="suporte">
      <i class="fas fa-headset"></i>
      <div class="mode-info">
        <span class="mode-name">Suporte</span>
        <span class="mode-desc">Atendimento ao Cliente</span>
      </div>
    </div>
  </div>
</div>
```

#### 2.2 Estilos Adaptativos
**Arquivo:** `WhatsappChrome2/sidepanel.css`
```css
/* Seletor de Modo */
.mode-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.mode-selector {
  display: flex;
  gap: 12px;
}

.mode-option {
  flex: 1;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.mode-option:hover {
  border-color: #007bff;
  background: #f8f9ff;
}

.mode-option.active {
  border-color: var(--primary-color, #007bff);
  background: var(--primary-color, #007bff);
  color: white;
}

.mode-info {
  display: flex;
  flex-direction: column;
}

.mode-name {
  font-weight: 600;
  font-size: 14px;
}

.mode-desc {
  font-size: 12px;
  opacity: 0.8;
}

/* Variáveis CSS por modo */
.sidepanel-container[data-mode="vendas"] {
  --primary-color: #25d366;
  --secondary-color: #128c7e;
}

.sidepanel-container[data-mode="suporte"] {
  --primary-color: #ff6b35;
  --secondary-color: #f7931e;
}
```

#### 2.3 JavaScript do Side Panel
**Arquivo:** `WhatsappChrome2/sidepanel.js`
```javascript
// Adicionar ao início do arquivo
let currentConfig = null;

// Função para carregar configuração
async function loadConfig() {
  const stored = await chrome.storage.sync.get(['whatsappConfig']);
  currentConfig = stored.whatsappConfig || DEFAULT_CONFIG;
  updateUIForMode(currentConfig.mode);
  return currentConfig;
}

// Função para salvar configuração
async function saveConfig(config) {
  await chrome.storage.sync.set({ whatsappConfig: config });
  currentConfig = config;
}

// Função para atualizar UI baseada no modo
function updateUIForMode(mode) {
  const container = document.querySelector('.sidepanel-container');
  const modeOptions = document.querySelectorAll('.mode-option');
  
  // Atualizar atributo data-mode
  container.setAttribute('data-mode', mode);
  
  // Atualizar seleção visual
  modeOptions.forEach(option => {
    option.classList.toggle('active', option.dataset.mode === mode);
  });
  
  // Atualizar título
  const config = currentConfig[mode];
  document.querySelector('.title').textContent = config.title;
}

// Event listeners para seletor de modo
document.addEventListener('DOMContentLoaded', async () => {
  await loadConfig();
  
  // Configurar event listeners para modo
  document.querySelectorAll('.mode-option').forEach(option => {
    option.addEventListener('click', async () => {
      const newMode = option.dataset.mode;
      currentConfig.mode = newMode;
      await saveConfig(currentConfig);
      updateUIForMode(newMode);
      
      // Notificar content script sobre mudança
      const tabs = await chrome.tabs.query({
        url: 'https://web.whatsapp.com/*'
      });
      
      tabs.forEach(tab => {
        chrome.tabs.sendMessage(tab.id, {
          type: 'MODE_CHANGED',
          mode: newMode,
          config: currentConfig[newMode]
        });
      });
    });
  });
});
```

### 3. Content Script

#### 3.1 Detecção Dinâmica de URL
**Arquivo:** `WhatsappChrome2/content.js`
```javascript
// Adicionar no início do arquivo
let extensionConfig = null;

// Função para carregar configuração
async function loadExtensionConfig() {
  const stored = await chrome.storage.sync.get(['whatsappConfig']);
  extensionConfig = stored.whatsappConfig || {
    mode: 'vendas',
    vendas: { baseUrl: 'https://admin.promokit.com.br/crm/whatsapp-assistant/' },
    suporte: { baseUrl: 'https://admin.promokit.com.br/suporte/whatsapp-assistant/' }
  };
  return extensionConfig;
}

// Função para obter URL baseada no modo atual
function getIframeUrl(telefone) {
  if (!extensionConfig) return null;
  
  const currentMode = extensionConfig.mode || 'vendas';
  const baseUrl = extensionConfig[currentMode].baseUrl;
  
  return `${baseUrl}${telefone}`;
}

// Modificar função injectIframe
async function injectIframe() {
  if (config.iframeInjected) return;
  
  // Carregar configuração antes de injetar
  await loadExtensionConfig();
  
  // ... resto da função permanece igual
  
  // Adicionar classe de modo ao container
  const container = document.getElementById('whatsapp-support-iframe-container');
  if (container && extensionConfig) {
    container.setAttribute('data-mode', extensionConfig.mode);
    container.style.setProperty('--primary-color', extensionConfig[extensionConfig.mode].primaryColor);
  }
}

// Listener para mudanças de modo
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'MODE_CHANGED') {
    extensionConfig = { ...extensionConfig, mode: message.mode };
    updateIframeForNewMode(message.mode, message.config);
  }
});

// Função para atualizar iframe quando modo mudar
function updateIframeForNewMode(mode, config) {
  const container = document.getElementById('whatsapp-support-iframe-container');
  const iframe = document.getElementById('whatsapp-support-iframe');
  
  if (container) {
    container.setAttribute('data-mode', mode);
    container.style.setProperty('--primary-color', config.primaryColor);
  }
  
  // Recarregar iframe com nova URL se houver telefone ativo
  const currentPhone = getCurrentPhoneNumber();
  if (iframe && currentPhone) {
    const newUrl = getIframeUrl(currentPhone);
    if (newUrl && iframe.src !== newUrl) {
      iframe.src = newUrl;
    }
  }
}
```

#### 3.2 Estilos Adaptativos no Content
**Arquivo:** `WhatsappChrome2/content.css`
```css
/* Variáveis CSS por modo */
#whatsapp-support-iframe-container[data-mode="vendas"] {
  --primary-color: #25d366;
  --secondary-color: #128c7e;
  --mode-icon: '\f201'; /* fa-chart-line */
}

#whatsapp-support-iframe-container[data-mode="suporte"] {
  --primary-color: #ff6b35;
  --secondary-color: #f7931e;
  --mode-icon: '\f7c4'; /* fa-headset */
}

/* Indicador de modo */
#whatsapp-support-iframe-container::before {
  content: attr(data-mode);
  position: absolute;
  top: 8px;
  right: 8px;
  background: var(--primary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  z-index: 1000;
}

/* Borda colorida baseada no modo */
#whatsapp-support-iframe-container {
  border-left: 4px solid var(--primary-color);
}

/* Botão de toggle com cor do modo */
.iframe-toggle-btn {
  background: var(--primary-color) !important;
}

.iframe-toggle-btn:hover {
  background: var(--secondary-color) !important;
}
```

---

## 🖥️ BACKEND - MÓDULO SUPORTE

### 4. Estrutura de Banco de Dados

#### 4.1 Tabelas Necessárias
**Arquivo:** `Servidor/sql/create.sql`
```sql
-- Tabela de grupos de suporte
CREATE TABLE grupo_suporte (
  id bigint NOT NULL AUTO_INCREMENT,
  nome varchar(255) NOT NULL,
  descricao text NULL,
  link_whatsapp varchar(500) NOT NULL,
  ativo bit(1) DEFAULT 1,
  cor varchar(7) DEFAULT '#ff6b35',
  empresa_id bigint NOT NULL,
  data_criacao datetime NOT NULL,
  data_atualizacao datetime NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_grupo_suporte_empresa FOREIGN KEY (empresa_id) REFERENCES empresa (id),
  INDEX idx_grupo_suporte_empresa (empresa_id),
  INDEX idx_grupo_suporte_ativo (ativo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Relacionamento contato-grupo de suporte
CREATE TABLE contato_grupo_suporte (
  id bigint NOT NULL AUTO_INCREMENT,
  contato_id bigint NOT NULL,
  grupo_suporte_id bigint NOT NULL,
  data_associacao datetime NOT NULL,
  ativo bit(1) DEFAULT 1,
  observacoes text NULL,
  empresa_id bigint NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_contato_grupo_contato FOREIGN KEY (contato_id) REFERENCES contato (id),
  CONSTRAINT fk_contato_grupo_suporte FOREIGN KEY (grupo_suporte_id) REFERENCES grupo_suporte (id),
  CONSTRAINT fk_contato_grupo_empresa FOREIGN KEY (empresa_id) REFERENCES empresa (id),
  UNIQUE KEY uk_contato_grupo_ativo (contato_id, grupo_suporte_id, ativo),
  INDEX idx_contato_grupo_contato (contato_id),
  INDEX idx_contato_grupo_suporte (grupo_suporte_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
```
