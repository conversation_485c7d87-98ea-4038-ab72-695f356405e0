import {
  ApplicationRef,
  Component,
  ElementRef,
  Inject,
  LOCALE_ID,
  OnInit,
  QueryList,
  ViewChild,
  ViewChildren
} from '@angular/core';
import {ITela} from "../../objeto/ITela";
import {PedidosService} from "../../services/pedidos.service";
import {CarrinhoService} from "../../services/carrinho.service";
import {PedidoLoja} from "../../objeto/PedidoLoja";

import {ModalEscolherEnderecoComponent} from "../modal-escolher-endereco/modal-escolher-endereco.component";
import {NgForm, NgModel} from "@angular/forms";
import {DominiosService} from "../../services/dominios.service";
import {ActivatedRoute, Router} from "@angular/router";
import {LojaCarrinhoComponent} from "../../loja-carrinho/loja-carrinho.component";
import {FormaDeEntrega} from "../../objeto/FormaDeEntrega";
import {ConstantsService} from "../../services/ConstantsService";
import {ClienteService} from "../../services/cliente.service";
import {EnderecoService} from "../../services/endereco.service";
import {DialogCloseResult, DialogService} from "@progress/kendo-angular-dialog";
import {MyDetectorDevice} from "../shared/MyDetectorDevice";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {PagseguroService} from "../../services/pagseguro.service";
import {SiteCampoAdicionalComponent} from "../site-campo-adicional/site-campo-adicional.component";
import {AdicionalUtils} from "../../objeto/AdicionalUtils";
import {DropDownFilterSettings} from "@progress/kendo-angular-dropdowns";
import {formatCurrency, Location} from "@angular/common";
import {PainelLoginComponent} from "../painel-login/painel-login.component";
import {CadContatoLojaComponent} from "../cad-contato/cad-contato-loja.component";
import {MaskedTextBoxComponent} from "@progress/kendo-angular-inputs";
import {
  FormasPagamentoLojaDesktopComponent
} from "../../loja-pagamento/formas-pagamentos/formas-pagamento-loja-desktop.component";
import {
  FormasPagamentoLojaDesktopNovaComponent
} from "../../loja-pagamento/formas-pagamentos/formas-pagamento-loja-desktop-nova";
import {ValidarContatoZapComponent} from "../cad-contato/validar-contato-zap/validar-contato-zap.component";
import {PixelTrackingService} from "../../services/pixel-tracking.service";
declare  var $;
declare var fbq;
declare var gtag;

@Component({
  selector: 'app-finalizar-pedido',
  templateUrl: './finalizar-pedido.component.html',
  styleUrls: ['./finalizar-pedido.component.scss']
})
export class FinalizarPedidoComponent implements OnInit ,  ITela {
  @ViewChild('txtTrocoM') txtTrocoM: ElementRef;
  @ViewChild('frm', {static: true}) frm: NgForm;
  @ViewChild('formasPagamentoAntigas', {static: false}) formasPagamentoAntigas: FormasPagamentoLojaDesktopComponent;
  @ViewChild('formasPagamentoNova', {static: false}) formasPagamentoNova: FormasPagamentoLojaDesktopNovaComponent;

  @ViewChild('panelCarrinho', {static: true}) panelCarrinho: LojaCarrinhoComponent;
  @ViewChild('kendoMaskedTextBox', {static: false}) txtTelefone: MaskedTextBoxComponent;
  resgate: any;
  pedido: PedidoLoja = new PedidoLoja();
  empresa: any = {};
  selecionou: boolean;
  buscando: any;
  eviandoPedido: boolean;
  msgErro: string
  msg: string
  urlWhatsapp: string
  FormaDeEntrega = FormaDeEntrega;
  informarCpf: boolean;
  cpfObrigatorio: boolean;
  informarDataNascimento: boolean;
  dataNascimentoObrigatorio: boolean;
  criarConta: boolean;
  exibirSenha: boolean;
  enderecos = [];
  isMobile = false;
  agendarEntrega: boolean;
  dataEntrega: Date;
  agora: Date = new Date();
  horaEntrega: any;
  agendouEntrega: boolean;
  apenasAgendamento: any;
  cashback: any;
  msgErroPagSeguro = '';
  valorTeste: any;
  camposAdicionais = [];
  formasDeEntrega = [];
  msgErroLink
  fidelidadeExterna: any;
  public filterSettings: DropDownFilterSettings = {
    caseSensitive: false,
    operator: 'contains'
  };
  @ViewChildren('adicionalComponent') ctrlAdicionais: QueryList<SiteCampoAdicionalComponent>;
  phoneMask = "(00) 00000-0000";
  numeroTelefone: any;
  fbqPurchase: any;
  gtagpurchase: any;
  constructor(private app: ApplicationRef, private pedidosService: PedidosService, private constantsService: ConstantsService,
              @Inject(LOCALE_ID) public locale: string,
              private activatedRoute: ActivatedRoute,      private location: Location,
              private  dominiosService: DominiosService, private router: Router, private clienteService: ClienteService,
              private  carrinhoService: CarrinhoService,    private autorizacao: AutorizacaoLojaService,
              private  enderecoService: EnderecoService, private dialogService: DialogService,
              private detectorDevice: MyDetectorDevice, private pagseguroService: PagseguroService,
              private autorizacaoLojaService: AutorizacaoLojaService,
              private pixelTrackingService: PixelTrackingService) {
  }

  ngOnInit() {
    this.fbqPurchase = null;
    this.gtagpurchase = null;
    this.isMobile = this.detectorDevice.isMobile();

    this.pedido = this.carrinhoService.obtenhaPedido();
    this.panelCarrinho.setModoPanel();

    this.carrinhoService.salvePedido(this.pedido);

    this.autorizacaoLojaService.sessao$.subscribe( (idSessao: string) => {
      if (typeof fbq !== 'undefined') {
        if (this.pedido && !this.pedido.disparouEventoIniciarCheckout) {
          this.pedido.disparouEventoIniciarCheckout = true;

          const idEvento = idSessao + '-' + new Date().getTime();
          this.carrinhoService.notifiqueInicioCheckout(this.pedido, idEvento);

          fbq('track', 'InitiateCheckout', {
            value: this.pedido.total,
            currency: 'BRL',
          }, {eventID: idEvento});
        }
      }
    });

    if (this.pedido.itens.length === 0) return this.abraTelaLoja();

    this.constantsService.empresa$.subscribe(data => {
      if (data) {
        this.empresa = data;
        this.apenasAgendamento = !this.empresa.estaRecebendoPedidos && this.empresa.permiteAgendamento
        this.empresa.apenasAgendamento = this.apenasAgendamento
        if(this.apenasAgendamento) this.agendarEntrega = true
        this.formasDeEntrega = this.obtenhaFormasDeEntrega(this.empresa.formasDeEntrega);

        if(this.empresa.integracaoFidelidade){
          this.fidelidadeExterna = Object.assign({},  this.empresa.integracaoFidelidade);

          if( !this.autorizacao.getUsuario()){
            this.fidelidadeExterna.aceitarFidelidade = true;
            this.fidelidadeExterna.optin = true;
          }
        }

        let formasAtivas = []

        for(let formaEntrega of this.formasDeEntrega)
          if(formaEntrega.ativa)
            formasAtivas.push(formaEntrega)

        if (formasAtivas.length === 1)
          this.pedido.entrega.formaDeEntrega = formasAtivas[0].nome;

        if(!this.apenasAgendamento)
          this.formasDeEntrega.forEach((formaDeEntrega: any) => {
            if( this.pedido.entrega.formaDeEntrega === formaDeEntrega.formaDeEntrega.nome ) {
              this.agendarEntrega = formaDeEntrega.agendamentoObrigatorio;
              this.apenasAgendamento = formaDeEntrega.agendamentoObrigatorio;
            }
          });

        this.setCashbackDoPagamento();

        if(this.pedido.dataEntrega) {
          this.agendouEntrega = true
          this.agendarEntrega = true
        }

        if(this.pedido.gerarLinkPagamento) this.gereLinkPagamento();

        setTimeout(() => {
          this.setFormasPagamentoExibir();
        }, 500)

        this.pedidosService.obtenhaAdicionaisPedido(this.empresa).then( (adicionais) => {
          this.camposAdicionais = adicionais;
          this.pedido.adicionais = {};
          this.pedido.camposAdicionais = adicionais;
          AdicionalUtils.prepareAdicionais(this.pedido, adicionais);
        });
      }
    })
    this.constantsService.campoCpf$.subscribe( campoCpf => {
      this.informarCpf = campoCpf != null;
      this.cpfObrigatorio = campoCpf && !campoCpf.opcional;
    });

    this.constantsService.campoDataNascimento$.subscribe( campoDataNascimento => {
      this.informarDataNascimento = campoDataNascimento != null;
      this.dataNascimentoObrigatorio = campoDataNascimento && !campoDataNascimento.opcional;
    });

    this.carrinhoService.alterouPedido.subscribe( (pedido) => {
       if(!pedido) return;

       if( this.cashback) {
         let totalSemTaxa = pedido.obtenhaTotalSemTaxaEntrega();
         if (this.cashback.minimoPedido > totalSemTaxa) {
           this.setPodeUsarCashback(totalSemTaxa);
         } else {
           this.setValorCashbachUsar(totalSemTaxa);
           this.cashback.podeUsarNoPedido = true;
         }
       }

       if(this.resgate)
         this.setUsarSaldoResgate()


    })
  }


  setFormasPagamentoExibir(){
    if(this.formasPagamentoNova)
      this.formasPagamentoNova.setFormasPagamento(this.empresa, this.pedido, this.pedido.pagamento, this.frm)

    if( this.formasPagamentoAntigas)
     this.formasPagamentoAntigas.setFormasPagamento(this.empresa, this.pedido, this.pedido.pagamento, this.frm)
  }

  setCashbackDasFormas(){
    if(this.formasPagamentoAntigas)
      this.formasPagamentoAntigas.cashback = this.cashback;

    if(this.formasPagamentoNova)
      this.formasPagamentoNova.cashback = this.cashback;

  }

  reabraAutenticacaoCartao(mensagem: string = null){
    if(this.formasPagamentoNova)
      this.formasPagamentoNova.trocarCartao(mensagem);

    if(this.formasPagamentoAntigas)
      this.formasPagamentoAntigas.trocarCartao(mensagem);
  }

  setValorCashbachUsar(valorPedidoPagar){
    this.cashback.valor = this.cashback.saldo >  valorPedidoPagar ? valorPedidoPagar : this.cashback.saldo;
  }

  setPodeUsarCashback(valorPedidoPagar: number){
    if(!this.cashback) return;
    this.cashback.podeUsarNoPedido = valorPedidoPagar >= this.cashback.minimoPedido;
    if(this.cashback.usarPadrao)
      if(this.cashback.saldo)
        this.cashback.usar =   this.cashback.podeUsar &&   this.cashback.podeUsarNoPedido;

    this.setValorCashbachUsar(valorPedidoPagar);
    this.alterouUsarSaldo()
  }

  setCashbackDoPagamento(){
    delete this.cashback;
    delete this.resgate;
    delete this.pedido.cashback;

    if(this.empresa.integracaoPedidoFidelidade || this.empresa.integracaoDelivery){
      this.clienteService.obtenhaSaldoResgate().then( (dados: any) => {
        if(!dados.resgate){
          this.cashback = dados;
          if(this.cashback){
            let valorPedido = this.pedido.obtenhaTotalSemTaxaEntrega();
            this.setPodeUsarCashback(valorPedido);
            this.setCashbackDasFormas();
          }
        } else {
          this.resgate = dados;

          if(this.resgate && this.pedido.totalResgatado)
            this.setUsarSaldoResgate();


        }



      })
    }
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu() {
    return false;
  }

  deveTerBordas() {
    return true;
  }



 async informouCpf(){
    if(this.pedido.contato.id && !this.pedido.contato.completarCadastro) return;

    let resposta = await   this.clienteService.obtenhaCadastroCompletarPeloCpf(this.pedido.contato.cpf).catch((e) => {
      console.error(e);
    });

    this.buscando = false;
    if (resposta  && resposta.id){
     this.pedido.contato.id = resposta.id
     this.pedido.contato.completarCadastro = true;
     this.criarConta = true;
    } else {
       if( this.pedido.contato.completarCadastro){
         delete  this.pedido.contato.id
         delete  this.pedido.contato.completarCadastro;
         this.criarConta = false;
       }
    }
  }

  informouTelefone() {
    this.buscando = true;
    this.clienteService.obtenhaClientePorTelefone(this.pedido.contato.telefone).then(resposta => {
      this.buscando = false;
      if (resposta  && resposta.id) {
         Object.assign(this.pedido.contato , resposta)
        if(this.pedido.contato.dataNascimento)
          this.pedido.contato.dataNascimento = new Date(this.pedido.contato.dataNascimento)
      } else {
        this.pedido.contato = {
          telefone: this.pedido.contato.telefone,
          codigoPais: this.pedido.contato.codigoPais
        }
      }

      if(resposta.fidelidadeExterna)
        this.setOptinFidelidadExterna( resposta.fidelidadeExterna)

      this.setCashbackDoPagamento();

    }).catch(() => {
      this.buscando = false;
    })
  }

  setOptinFidelidadExterna(fidelidadeExterna: any){
    this.fidelidadeExterna.aceitarFidelidade = fidelidadeExterna.aceitarFidelidade
    this.fidelidadeExterna.id_cliente = fidelidadeExterna.id_cliente
    this.fidelidadeExterna.optin = this.fidelidadeExterna.aceitarFidelidade;
  }



  abraModalEscolherEndereco() {
    let altura = window.innerHeight - 100;

    if( this.isMobile ) {
      altura = window.innerHeight;
    }

    const windowRef = this.dialogService.open({
      title: 'Cadastro de Endereço',
      content: ModalEscolherEnderecoComponent,
      minWidth: 250,
      width: window.innerWidth > 600 ? 600 : window.innerWidth,
      maxHeight: altura
    });

    const telaProduto: ModalEscolherEnderecoComponent = windowRef.content.instance;
    telaProduto.window = windowRef;
    telaProduto.pedido = this.pedido;

    document.body.style.overflow = "hidden";
    document.body.style.paddingRight = "17px";
    const $elemento: HTMLElement = document.querySelector('.carrinho_desktop .is-sticky');

    if( $elemento ) $elemento.style.paddingRight = '17px';

    windowRef.result.subscribe(async (endereco: any) => {
      document.body.style.overflow = "";
      document.body.style.paddingRight = "";
      if( $elemento ) $elemento.style.paddingRight = '';
      if (endereco && endereco.cidade) {
        this.pedido.entrega.setTaxaEntrega(endereco, null);
        this.pedido.calculeTotal();

        this.pedido.novosEnderecos.push(endereco);
        this.carrinhoService.salvePedido(this.pedido);

        let codigopromo = this.pedido.cupom ? this.pedido.cupom.codigo : null;

        if(codigopromo)
          await     this.carrinhoService.atualizeValorDesconto(codigopromo, this.empresa);

      }
    });
  }

  alterouFormaEntrega(formaEntrega: any) {
    if (this.pedido.entrega.ehRetirada()) {
      this.pedido.entrega.taxaDeEntrega = 0;
    } else if (this.pedido.entrega.endereco) {
      this.pedido.entrega.taxaDeEntrega = this.pedido.entrega.endereco.taxaDeEntrega;
    }

    if(!this.empresa.apenasAgendamento) {
      this.agendarEntrega = formaEntrega.agendamentoObrigatorio;
      this.apenasAgendamento = formaEntrega.agendamentoObrigatorio;
    }

    this.pedido.calculeTotal();

    if(this.pedido.temValorMinimoDaFormaEscolhida(this.formasDeEntrega)){
      if (this.pedido.entrega.ehDelivery() && !this.pedido.entrega.endereco) {
        if(!this.pedido.ultrapassouValorMaximoDaFormaEscolhida(this.formasDeEntrega))
          this.abraModalEscolherEndereco();
      }
    }

    this.carrinhoService.salvePedido(this.pedido);

  }

  selecioneTudo() {
    if (this.pedido.pagamento.trocoPara > 0) {
      this.txtTrocoM.nativeElement.select();
      this.selecionou = true;
    }
  }

  limpeSeTiverFoco(valor: any) {
    if (!this.selecionou)
      return;

    this.selecionou = false;
    this.pedido.pagamento.trocoPara = 0;
  }

  gereLinkPagamento(){
    delete  this.msgErroLink;
    $("#alertaGerandoPagamento").modal();
    this.pedidosService.gereLinkPagamento(this.pedido).then((res: any) => {
      this.pedido.linkPagamento = res.link;
      this.carrinhoService.salvePedido(this.pedido);
      window.location.href = res.link;
    }).catch((erro) => {
      console.log(erro)
      alert(erro)
      this.msgErroLink = erro;
    });
  }

  facaLoginGuest(resposta: any){
    if(!resposta.fezLoginGuest) return Promise.resolve();

    return this.autorizacao.atualizeUsuarioLogado().then( () => {
      Promise.resolve()
    });
  }


  posicioneNoComplemento(campoAdicional) {
    /*
    const $controleAdicional = document.getElementById('adicional_' + campoAdicional.id);

    if( !$controleAdicional ) {
      return;
    }

    const topo = $controleAdicional.offsetTop - 10;

    document.querySelector('.k-dialog-content').scrollTo(0, topo);
     */
  }

  fazerPedido() {
    this.msgErro = null;
    if(this.eviandoPedido) return;

    if (this.frm.valid) {
      if(this.pedido.validarValorPedido()){
          let totalPagar = this.pedido.obtenhaValorAhPagar();

          if(totalPagar !== this.pedido.pagamento.dadosCartao.valorPagamento){
            this.msgErro = 'Valor pedido foi alterado, será necessário uma nova autenticação do cartão'
            setTimeout(() => {
              this.reabraAutenticacaoCartao(this.msgErro);
            })
            return;
          }
      }

      this.eviandoPedido = true;

      if(!this.agendarEntrega) {
        this.pedido.dataEntrega = null;
        this.pedido.horarioEntrega = null;
      }

      if(this.fidelidadeExterna){
        if(this.fidelidadeExterna.optin){
          this.pedido.fidelidadeGcom = { id_cliente: this.fidelidadeExterna.id_cliente}
        } else {
          delete   this.pedido.fidelidadeGcom
        }
      }

      this.pedidosService.salvePedido(this.pedido.obtenhaDadosEnvio(this.empresa)).then((resposta) => {
        this.pedido.codigo = resposta.codigo;
        this.pedido.guid = resposta.guid;
        this.eviandoPedido = false;
        this.panelCarrinho.finalizouPedido();

        if(this.pedido.usandoCashbackNoPagamento())
          localStorage.recarregarSaldo =  true;

        this.pixelTrackingService.trackPurchase(this.pedido, resposta.codigo, resposta.id, resposta.guid);

        this.facaLoginGuest(resposta).then( () => {
          if(resposta.gerarLinkPagamento) {
            this.pedido.gerarLinkPagamento = true;
            this.carrinhoService.salvePedido(this.pedido);
            this.gereLinkPagamento();
          } else {

            if(resposta.abrirUrl) //esse caso só ocorre em cardápios do módulo '
              window.open(  this.urlWhatsapp );
            else if(!this.pedido.mesa || !this.pedido.mesa.id) //Se não houver mesa, não abre o whatsapp
              window.open(resposta.urlAbrir)

            let state: any = { empresa: this.empresa, urlAbrir: resposta.urlAbrir,
              falhaNoPagamentoOnline: resposta.falhaNoPagamentoOnline,
              mensagemFalhaPagamento: resposta.mensagemFalhaPagamento
            };

            if(resposta.aguardandoTokenizar)
              state.aguardandoTokenizar = resposta.aguardandoTokenizar

            this.carrinhoService.salveUltimoPedido(state);
            this.dominiosService.vaParaTelaAcompanharPedido(this.pedido);
          }
        })


      }).catch((erro) => {
        this.eviandoPedido = false;
        this.msgErro = erro;
      });
    } else {
      this.msgErro = 'Preencha os campos obrigatórios'

      setTimeout(() => {

        const $erros: any = document.getElementsByClassName('invalid-feedback');

        if($erros && $erros.length){
          let elmentoErro =  $erros[0];
          if(elmentoErro.parentElement.className.indexOf('input-group') >= 0)
            elmentoErro = elmentoErro.parentElement

          if(elmentoErro.parentElement.className.indexOf('form-group') >= 0)
            elmentoErro = elmentoErro.parentElement

          const topo =  elmentoErro.offsetTop - 40;
          window.scrollTo(0, topo);
        }


      }, 50)
    }
  }

  novoPedido() {
    this.abraTelaLoja();

  }

  abraTelaLoja() {
    this.dominiosService.vaParaHome()
  }

  exibirCriarConta() {
    this.criarConta = true;
  }

  exibirSenhaTela() {
    this.exibirSenha = !this.exibirSenha;
  }

  canceleCriarConta() {
    this.criarConta = false;
  }

  obtenhaTotalPagar() {
    if(!this.cashback || !this.cashback.usar)
      return this.pedido.total;

    let totalPagar = this.pedido.total - this.cashback.valor;

    if(totalPagar < 0) totalPagar = 0;

    return Number(totalPagar.toFixed(2));
  }

  setUsarSaldoResgate(){
    if(!this.resgate) return;

    if(this.resgate.saldo && this.pedido.totalResgatado){
      this.resgate.valor  = this.pedido.totalResgatado
      this.pedido.resgate = this.resgate;
    }
  }

  alterouUsarSaldo() {
    if(this.cashback && this.cashback.usar){
      if( this.cashback.saldo  ){
        this.pedido.cashback = this.cashback;
        this.pedido.calculeTotal();
        this.pedido.calculeTroco();
      } else {
        let usuario = this.autorizacao.getUsuario();

        if(!usuario){
          if(this.pedido.contato.id)
            this.abraModalLogin();
          else
            CadContatoLojaComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
              this.dialogService, this.pedido.contato);

        } else {
          if(this.cashback.fazerOptin){
            this.abraModalFazerOptin();
          } else {
            if(!usuario.cpf)
              this.abraModalAtualizarCadastro();
          }
        }
      }
    } else {
      delete this.pedido.cashback
      this.pedido.calculeTotal();
    }

    this.carrinhoService.salvePedido(this.pedido);

    setTimeout(() => {
      this.setFormasPagamentoExibir();
      this.setCashbackDasFormas();
    })
  }

  abraModalFazerOptin(){
    this.pedido.contato.fazerOptin = true;
    CadContatoLojaComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
      this.dialogService, this.pedido.contato);

    return false;
  }

  abraModalLogin(){
    let telefone = this.pedido.contato ? this.pedido.contato.telefone : null;
    PainelLoginComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
      this.dialogService, telefone , (result) => {
        if(result && result.login){
          let usuario = this.autorizacao.getUsuario();

          if(usuario && usuario.id){
            this.pedido.contato = usuario;
            this.carrinhoService.salvePedido(this.pedido);
            this.setCashbackDoPagamento()
          }
        }
      });

    return false;
  }

  abraModalValidarConta(){
    ValidarContatoZapComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
      this.dialogService,  this.pedido.contato , (result) => {
        if(result && result.login) {
          this.setCashbackDoPagamento();
        }
      });

    return false;
  }

  abraModalAtualizarCadastro(){
    this.pedido.contato.atualizarCadastro = true;
    CadContatoLojaComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
      this.dialogService, this.pedido.contato, (result) => {
        if(result && result.id){
          this.setCashbackDoPagamento()
        }
      }) ;

    return false;
  }


  private obtenhaFormasDeEntrega(formasDeEntrega: Array<any>) {
    const formas = [];

    for(let formaEntrega of formasDeEntrega) {
      if( !this.pedido.entrega.permiteEntrega ) {
        if( formaEntrega.formaDeEntrega.nome === FormaDeEntrega.RECEBER_EM_CASA ) {
          continue;
        }
      }

      formas.push(formaEntrega);

      if(formaEntrega.permiteComerNoLocal){
        let formaComerNoLocal = Object.assign({}, formaEntrega);

        formaComerNoLocal.comerNoLocal = true;
        formaComerNoLocal.nome = 'Comer no local';

        formas.push(formaComerNoLocal)
      }
    }

    return formas;
  }



  deveExibirBannerTema() {
    return false;
  }

  alterouOptinFidelidade(){
  //  this.criarConta = this.cashback.optin;
  }

  obtenhaSaldo() {
    if(!this.cashback) return "";

    let descricao = formatCurrency(this.cashback.saldo, this.locale, "R$")

    if(this.empresa.integracaoFidelidade && this.empresa.integracaoFidelidade.sistema === 'gcom')
      descricao = descricao.replace('R$', this.empresa.idRede === 2 ? "R$G" : "R$C")


    return descricao;
  }



  exibirDadosCliente(){
    let usuario = this.autorizacao.getUsuario();

    if(!usuario || !usuario.id) return true;

    if(this.informarCpf) return  !usuario.cpf

    return  false;
  }

  onCountrySelected($event: any) {
    this.pedido.contato.codigoPais = $event;
    console.log("setou código país. Contato: ", this.pedido.contato)
  }

  onPhoneMaskChange($event: string) {
    this.phoneMask = $event;
    if(this.txtTelefone)
      (this.txtTelefone as any).updateValue(this.txtTelefone.input.nativeElement.value);
  }


  desmarcouNovaOpcao(opcao: any){
    this.ctrlAdicionais.forEach((ctlAdicioanal: SiteCampoAdicionalComponent) => {
      ctlAdicioanal.desmarcouOpcaoQueDepende(opcao)
    });
  }


  escolheuNovaOpcao(opcao: any){
    if(this.ctrlAdicionais){
      this.ctrlAdicionais.forEach((ctlAdicioanal: SiteCampoAdicionalComponent) => {
        ctlAdicioanal.exibaOuEscondaOpcaoQueDepende(opcao)
      });
    }
  }


}

